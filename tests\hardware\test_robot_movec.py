# tests/hardware/test_robot_movec.py

import sys
import os
import time
import math

# --- 项目路径设置 ---
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# --- 导入 ---
from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface
try:
    import nrc_interface as nrc
    print("成功导入nrc_interface模块")
except ImportError as e:
    print(f"导入nrc_interface失败: {e}")
    exit()

class RobotMovecTester:
    """专门用于测试机器人圆弧运动功能的类"""

    def __init__(self, robot_interface: RobotInterface):
        self.robot = robot_interface
        self.socket_fd = robot_interface.socket_fd

    def robot_initialize_and_power_on(self):
        """健壮的机器人初始化和上电流程"""
        print("\n--- 开始机器人初始化和上电流程 ---")
        try:
            # 步骤1: 清除错误
            print("步骤 1: 清除错误...")
            nrc.clear_error(self.socket_fd)
            time.sleep(0.5)

            # 步骤2: 获取当前伺服状态
            print("步骤 2: 获取当前伺服状态...")
            result = nrc.get_servo_state(self.socket_fd, 0)
            
            if isinstance(result, list) and len(result) > 1:
                ret_code = result[0]
                current_state = result[1]
            else:
                print(f"错误: 获取伺服状态返回格式不正确: {result}")
                return False

            if ret_code != 0:
                print(f"错误: 获取伺服状态API调用失败，返回码: {ret_code}")
                return False
                
            print(f"  -> 当前伺服状态为 {current_state}")

            # 步骤3: 根据状态进入就绪态(1)
            if current_state == 0:
                nrc.set_servo_state(self.socket_fd, 1)
                time.sleep(0.5)
            elif current_state == 3:
                nrc.set_servo_poweroff(self.socket_fd)
                time.sleep(1)

            # 步骤4: 执行上电操作
            print("步骤 4: 执行上电操作...")
            ret_code = nrc.set_servo_poweron(self.socket_fd)
            if ret_code != 0:
                print(f"上电失败！返回码: {ret_code}。请检查安全回路/急停/远程模式。")
                return False

            time.sleep(1)
            
            # 最后再次确认状态
            result = nrc.get_servo_state(self.socket_fd, 0)
            final_state = result[1] if isinstance(result, list) and len(result) > 1 else -1

            if final_state == 3:
                print(f"✅ 成功！机器人已上电，进入运行状态 (3)。")
                return True
            else:
                print(f"错误: 上电后状态异常，当前: {final_state}")
                return False

        except Exception as e:
            print(f"初始化和上电流程中发生异常: {e}")
            return False

    def get_cartesian_position(self):
        """获取并返回当前的笛卡尔坐标列表"""
        try:
            cart_pos_container = nrc.VectorDouble()
            ret_code = nrc.get_current_position(self.socket_fd, 1, cart_pos_container)
            if ret_code == 0:
                return [cart_pos_container[i] for i in range(len(cart_pos_container))]
            else:
                print(f"获取笛卡尔位置失败，返回码: {ret_code}")
                return None
        except Exception as e:
            print(f"获取笛卡尔位置时发生异常: {e}")
            return None

    def calculate_arc_points(self, center_pos, radius=10.0, start_angle=0, arc_angle=90):
        """
        计算圆弧运动的三个关键点
        
        Args:
            center_pos (list): 当前位置作为圆弧中心附近的参考点 [X, Y, Z, RX, RY, RZ]
            radius (float): 圆弧半径 (mm)
            start_angle (float): 起始角度 (度)
            arc_angle (float): 圆弧角度 (度)
            
        Returns:
            tuple: (起始点, 中间点, 结束点) 的位置列表
        """
        # 将角度转换为弧度
        start_rad = math.radians(start_angle)
        mid_rad = math.radians(start_angle + arc_angle / 2)
        end_rad = math.radians(start_angle + arc_angle)
        
        # 基于当前位置计算圆弧点，在XY平面内进行圆弧运动
        base_x, base_y, base_z = center_pos[0], center_pos[1], center_pos[2]
        
        # 起始点
        start_point = list(center_pos)
        start_point[0] = base_x + radius * math.cos(start_rad)
        start_point[1] = base_y + radius * math.sin(start_rad)
        
        # 中间点
        mid_point = list(center_pos)
        mid_point[0] = base_x + radius * math.cos(mid_rad)
        mid_point[1] = base_y + radius * math.sin(mid_rad)
        
        # 结束点
        end_point = list(center_pos)
        end_point[0] = base_x + radius * math.cos(end_rad)
        end_point[1] = base_y + radius * math.sin(end_rad)
        
        return start_point, mid_point, end_point

    def check_position_safety(self, position, max_range=30.0):
        """
        检查位置是否在安全范围内
        
        Args:
            position (list): 位置坐标 [X, Y, Z, RX, RY, RZ]
            max_range (float): 最大允许范围
            
        Returns:
            bool: 是否安全
        """
        for i in range(3):  # 只检查X, Y, Z坐标
            if abs(position[i]) > max_range:
                return False
        return True

    def run_arc_motion_test(self):
        """执行圆弧运动测试"""
        print("\n--- 开始执行圆弧运动测试 ---")

        # 1. 获取当前位置
        current_pos = self.get_cartesian_position()
        if not current_pos:
            print("错误：无法获取当前位置，测试中止。")
            return False
        
        print(f"1. 当前位姿 (X,Y,Z,A,B,C): {[f'{p:.3f}' for p in current_pos]}")
        
        # 2. 计算圆弧运动的三个点
        radius = 15.0  # 15mm半径，确保在30mm范围内
        start_point, mid_point, end_point = self.calculate_arc_points(
            current_pos, radius=radius, start_angle=0, arc_angle=90
        )
        
        print(f"2. 计算的圆弧点:")
        print(f"   起始点: {[f'{p:.3f}' for p in start_point]}")
        print(f"   中间点: {[f'{p:.3f}' for p in mid_point]}")
        print(f"   结束点: {[f'{p:.3f}' for p in end_point]}")
        
        # 3. 安全检查
        for i, (name, point) in enumerate([("起始点", start_point), ("中间点", mid_point), ("结束点", end_point)]):
            if not self.check_position_safety(point):
                print(f"错误：{name} 超出安全范围 (±30mm)，测试中止。")
                return False
        
        print("3. ✅ 所有点都在安全范围内")
        
        # 4. 执行圆弧运动
        try:
            print("\n4. 开始执行圆弧运动...")
            
            # 创建VectorDouble对象
            pos1 = nrc.VectorDouble(start_point)
            pos2 = nrc.VectorDouble(mid_point)
            pos3 = nrc.VectorDouble(end_point)
            
            # 设置运动参数
            vel = 20      # 速度 (mm/s)
            coord = 1     # 笛卡尔坐标系
            acc = 20      # 加速度
            dec = 20      # 减速度
            
            print(f"   运动参数: 速度={vel}mm/s, 加速度={acc}, 减速度={dec}")
            
            # 调用圆弧运动函数
            ret_code = nrc.robot_movec(self.socket_fd, pos1, pos2, pos3, vel, coord, acc, dec)
            print(f"   圆弧运动指令发送完成，返回码: {ret_code}")
            
            if ret_code == 0:
                print("   ✅ 圆弧运动指令发送成功！")
                
                # 等待运动完成
                print("   等待圆弧运动完成...")
                time.sleep(5)  # 给足够时间完成运动
                
                # 获取最终位置
                final_pos = self.get_cartesian_position()
                if final_pos:
                    print(f"5. ✅ 圆弧运动完成！最终位姿: {[f'{p:.3f}' for p in final_pos]}")
                    return True
                else:
                    print("错误：无法获取最终位姿。")
                    return False
            else:
                print(f"   ❌ 圆弧运动指令发送失败，返回码: {ret_code}")
                return False
                
        except Exception as e:
            print(f"圆弧运动执行中发生异常: {e}")
            return False

    def robot_power_off(self):
        """机器人安全下电"""
        print("\n--- 开始机器人下电流程 ---")
        try:
            nrc.set_servo_poweroff(self.socket_fd)
            time.sleep(1)
            print("✅ 机器人已安全下电。")
        except Exception as e:
            print(f"下电流程中发生异常: {e}")


def main():
    """主测试函数"""
    robot = None
    print("=" * 60)
    print("           机 器 人 圆 弧 运 动 测 试")
    print("=" * 60)
    print("\n⚠️  警告：请确保机器人周围有足够的安全空间！")
    print("    本测试将执行半径15mm的90度圆弧运动")
    print("    所有运动点都将限制在±30mm范围内\n")
    time.sleep(3)

    try:
        # 连接机器人
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        movec_tester = RobotMovecTester(robot)

        # 步骤1: 初始化并上电
        if not movec_tester.robot_initialize_and_power_on():
            print("\n❌ 机器人未能成功上电，圆弧运动测试无法继续。")
            return

        # 步骤2: 执行圆弧运动测试
        success = movec_tester.run_arc_motion_test()
        
        if success:
            print("\n🎉 圆弧运动测试成功完成！")
        else:
            print("\n❌ 圆弧运动测试失败。")

    except Exception as e:
        print(f"\n❌ 测试过程中发生意外错误: {e}")
    finally:
        # 步骤3: 确保机器人下电并断开连接
        if robot:
            if nrc.get_connection_status(robot.socket_fd) == 0:
                movec_tester.robot_power_off()
            robot.disconnect()
        print("\n测试结束。")

if __name__ == "__main__":
    main()
