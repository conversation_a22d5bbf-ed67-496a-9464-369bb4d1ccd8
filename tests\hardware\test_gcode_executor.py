# tests/hardware/test_gcode_executor.py

import sys
import os
import time
import re
from typing import List, Dict, Optional, Tuple

# --- 项目路径设置 ---
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)
LIB_PATH = os.path.join(PROJECT_ROOT, 'lib')
if LIB_PATH not in sys.path:
    sys.path.insert(0, LIB_PATH)

# --- 导入 ---
from config import ROBOT_IP, ROBOT_PORT
from src.hardware.robot_interface import RobotInterface
try:
    import nrc_interface as nrc
    print("成功导入nrc_interface模块")
except ImportError as e:
    print(f"导入nrc_interface失败: {e}")
    exit()

class GCodeCommand:
    """G-code命令数据结构"""
    def __init__(self, command_type: str, x: float = None, y: float = None, z: float = None,
                 a: float = None, b: float = None, c: float = None, f: float = None, e: float = None):
        self.command_type = command_type  # G0, G1, etc.
        self.x = x
        self.y = y
        self.z = z
        self.a = a  # A轴旋转角度
        self.b = b  # B轴旋转角度
        self.c = c  # C轴旋转角度
        self.f = f  # 进给速度
        self.e = e  # 挤出量（3D打印用）
        
    def __str__(self):
        coords = []
        if self.x is not None: coords.append(f"X{self.x:.3f}")
        if self.y is not None: coords.append(f"Y{self.y:.3f}")
        if self.z is not None: coords.append(f"Z{self.z:.3f}")
        if self.a is not None: coords.append(f"A{self.a:.3f}")
        if self.b is not None: coords.append(f"B{self.b:.3f}")
        if self.c is not None: coords.append(f"C{self.c:.3f}")
        if self.f is not None: coords.append(f"F{self.f:.0f}")
        return f"{self.command_type} {' '.join(coords)}"

class GCodeParser:
    """G-code文件解析器"""
    
    @staticmethod
    def parse_gcode_file(file_path: str) -> List[GCodeCommand]:
        """
        解析G-code文件
        
        Args:
            file_path (str): G-code文件路径
            
        Returns:
            List[GCodeCommand]: 解析后的G-code命令列表
        """
        commands = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()
                    
                    # 跳过空行和注释行
                    if not line or line.startswith(';'):
                        continue
                    
                    # 移除行内注释
                    if ';' in line:
                        line = line.split(';')[0].strip()
                    
                    # 解析G-code命令
                    command = GCodeParser.parse_gcode_line(line)
                    if command:
                        commands.append(command)
                        
        except FileNotFoundError:
            print(f"错误：找不到G-code文件: {file_path}")
            return []
        except Exception as e:
            print(f"解析G-code文件时发生错误: {e}")
            return []
            
        print(f"成功解析G-code文件，共 {len(commands)} 条有效命令")
        return commands
    
    @staticmethod
    def parse_gcode_line(line: str) -> Optional[GCodeCommand]:
        """
        解析单行G-code命令
        
        Args:
            line (str): G-code命令行
            
        Returns:
            Optional[GCodeCommand]: 解析后的命令对象，如果解析失败返回None
        """
        # 匹配G-code命令类型
        command_match = re.search(r'(G[0-9]+)', line.upper())
        if not command_match:
            return None
            
        command_type = command_match.group(1)
        
        # 只处理移动命令
        if command_type not in ['G0', 'G1']:
            return None
        
        # 提取坐标和参数
        x = GCodeParser.extract_value(line, 'X')
        y = GCodeParser.extract_value(line, 'Y')
        z = GCodeParser.extract_value(line, 'Z')
        a = GCodeParser.extract_value(line, 'A')
        b = GCodeParser.extract_value(line, 'B')
        c = GCodeParser.extract_value(line, 'C')
        f = GCodeParser.extract_value(line, 'F')
        e = GCodeParser.extract_value(line, 'E')
        
        return GCodeCommand(command_type, x, y, z, a, b, c, f, e)
    
    @staticmethod
    def extract_value(line: str, param: str) -> Optional[float]:
        """从G-code行中提取指定参数的值"""
        pattern = rf'{param}([-+]?\d*\.?\d+)'
        match = re.search(pattern, line.upper())
        return float(match.group(1)) if match else None

class GCodeExecutor:
    """G-code执行器"""

    def __init__(self, robot_interface: RobotInterface):
        self.robot = robot_interface
        self.socket_fd = robot_interface.socket_fd
        self.origin_offset = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]  # G-code原点偏移
        self.current_position = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]  # 当前位置
        self.max_work_range = 0.100  # 最大工作范围 (m) - 100mm转换为0.1m
        self.mm_to_m_scale = 0.001  # 毫米到米的转换比例
        
    def robot_initialize_and_power_on(self):
        """机器人初始化和上电"""
        print("\n--- 开始机器人初始化和上电流程 ---")
        try:
            # 清除错误
            print("步骤 1: 清除错误...")
            nrc.clear_error(self.socket_fd)
            time.sleep(0.5)

            # 获取当前伺服状态
            print("步骤 2: 获取当前伺服状态...")
            result = nrc.get_servo_state(self.socket_fd, 0)
            
            if isinstance(result, list) and len(result) > 1:
                ret_code = result[0]
                current_state = result[1]
            else:
                print(f"错误: 获取伺服状态返回格式不正确: {result}")
                return False

            if ret_code != 0:
                print(f"错误: 获取伺服状态API调用失败，返回码: {ret_code}")
                return False
                
            print(f"  -> 当前伺服状态为 {current_state}")

            # 根据状态进入就绪态
            if current_state == 0:
                nrc.set_servo_state(self.socket_fd, 1)
                time.sleep(0.5)
            elif current_state == 3:
                nrc.set_servo_poweroff(self.socket_fd)
                time.sleep(1)

            # 执行上电操作
            print("步骤 3: 执行上电操作...")
            ret_code = nrc.set_servo_poweron(self.socket_fd)
            if ret_code != 0:
                print(f"上电失败！返回码: {ret_code}。请检查安全回路/急停/远程模式。")
                return False

            time.sleep(1)
            
            # 确认最终状态
            result = nrc.get_servo_state(self.socket_fd, 0)
            final_state = result[1] if isinstance(result, list) and len(result) > 1 else -1

            if final_state == 3:
                print(f"✅ 成功！机器人已上电，进入运行状态 (3)。")
                return True
            else:
                print(f"错误: 上电后状态异常，当前: {final_state}")
                return False

        except Exception as e:
            print(f"初始化和上电流程中发生异常: {e}")
            return False
    
    def get_cartesian_position(self):
        """获取当前笛卡尔位置"""
        try:
            cart_pos_container = nrc.VectorDouble()
            ret_code = nrc.get_current_position(self.socket_fd, 1, cart_pos_container)
            if ret_code == 0:
                return [cart_pos_container[i] for i in range(len(cart_pos_container))]
            else:
                print(f"获取笛卡尔位置失败，返回码: {ret_code}")
                return None
        except Exception as e:
            print(f"获取笛卡尔位置时发生异常: {e}")
            return None
    
    def move_to_home(self):
        """移动到零点位置"""
        print("\n--- 移动机器人到零点位置 ---")
        try:
            ret_code = nrc.robot_go_home(self.socket_fd)
            if ret_code == 0:
                print("✅ 机器人成功移动到零点位置")
                time.sleep(3)  # 等待运动完成
                
                # 获取零点位置
                home_pos = self.get_cartesian_position()
                if home_pos:
                    self.current_position = home_pos
                    print(f"零点位置: {[f'{p:.3f}' for p in home_pos]}")
                    return True
            else:
                print(f"移动到零点失败，返回码: {ret_code}")
                return False
        except Exception as e:
            print(f"移动到零点时发生异常: {e}")
            return False
    
    def set_gcode_origin(self, first_gcode_point: GCodeCommand):
        """设置G-code坐标系原点（考虑mm到m的单位转换）"""
        print("\n--- 设置G-code坐标系原点 ---")

        # 获取当前机器人位置作为G-code原点
        robot_pos = self.get_cartesian_position()
        if not robot_pos:
            print("错误：无法获取当前机器人位置")
            return False

        # 计算偏移量：机器人坐标(m) - G-code坐标转换为米
        self.origin_offset[0] = robot_pos[0] - ((first_gcode_point.x or 0) * self.mm_to_m_scale)
        self.origin_offset[1] = robot_pos[1] - ((first_gcode_point.y or 0) * self.mm_to_m_scale)
        self.origin_offset[2] = robot_pos[2] - ((first_gcode_point.z or 0) * self.mm_to_m_scale)
        self.origin_offset[3] = robot_pos[3] - (first_gcode_point.a or 0)
        self.origin_offset[4] = robot_pos[4] - (first_gcode_point.b or 0)
        self.origin_offset[5] = robot_pos[5] - (first_gcode_point.c or 0)

        print(f"G-code原点设置完成:")
        print(f"  机器人当前位置(m): {[f'{p:.6f}' for p in robot_pos]}")
        print(f"  G-code第一点(mm): {first_gcode_point}")
        print(f"  G-code第一点转换(m): X={((first_gcode_point.x or 0) * self.mm_to_m_scale):.6f}, Y={((first_gcode_point.y or 0) * self.mm_to_m_scale):.6f}, Z={((first_gcode_point.z or 0) * self.mm_to_m_scale):.6f}")
        print(f"  坐标偏移量(m): {[f'{p:.6f}' for p in self.origin_offset]}")

        return True

    def gcode_to_robot_coordinates(self, gcode_cmd: GCodeCommand) -> List[float]:
        """将G-code坐标转换为机器人坐标（包含mm到m的单位转换）"""
        robot_coords = list(self.current_position)  # 从当前位置开始

        # 转换位置坐标（X, Y, Z从毫米转换为米）
        if gcode_cmd.x is not None:
            robot_coords[0] = (gcode_cmd.x * self.mm_to_m_scale) + self.origin_offset[0]
        if gcode_cmd.y is not None:
            robot_coords[1] = (gcode_cmd.y * self.mm_to_m_scale) + self.origin_offset[1]
        if gcode_cmd.z is not None:
            robot_coords[2] = (gcode_cmd.z * self.mm_to_m_scale) + self.origin_offset[2]

        # 姿态角度不需要单位转换（已经是度数）
        if gcode_cmd.a is not None:
            robot_coords[3] = gcode_cmd.a + self.origin_offset[3]
        if gcode_cmd.b is not None:
            robot_coords[4] = gcode_cmd.b + self.origin_offset[4]
        if gcode_cmd.c is not None:
            robot_coords[5] = gcode_cmd.c + self.origin_offset[5]

        return robot_coords

    def check_position_safety(self, target_pos: List[float]) -> bool:
        """检查目标位置是否在安全工作范围内（米单位）"""
        # 获取当前机器人位置作为参考
        current_robot_pos = self.get_cartesian_position()
        if not current_robot_pos:
            print("警告：无法获取当前机器人位置进行安全检查")
            return False

        # 计算相对于当前位置的偏移
        for i in range(3):  # 只检查X, Y, Z坐标
            offset = abs(target_pos[i] - current_robot_pos[i])
            if offset > self.max_work_range:
                print(f"警告：坐标轴{['X', 'Y', 'Z'][i]}偏移 {offset:.6f}m ({offset*1000:.3f}mm) 超出安全范围 ±{self.max_work_range}m (±{self.max_work_range*1000:.0f}mm)")
                return False
        return True

    def execute_gcode_command(self, gcode_cmd: GCodeCommand) -> bool:
        """执行单个G-code命令"""
        # 转换为机器人坐标
        target_pos = self.gcode_to_robot_coordinates(gcode_cmd)

        # 显示坐标转换信息
        print(f"  G-code坐标(mm): X={gcode_cmd.x}, Y={gcode_cmd.y}, Z={gcode_cmd.z}")
        print(f"  机器人坐标(m): X={target_pos[0]:.6f}, Y={target_pos[1]:.6f}, Z={target_pos[2]:.6f}")

        # 安全检查
        if not self.check_position_safety(target_pos):
            print(f"跳过不安全的命令: {gcode_cmd}")
            return False

        try:
            # 创建运动命令
            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 1  # 笛卡尔坐标
            move_cmd.targetPosValue = nrc.VectorDouble(target_pos)
            move_cmd.coord = 1  # 笛卡尔坐标系
            move_cmd.pl = 1  # 平滑度

            # 设置运动参数
            if gcode_cmd.command_type == 'G0':  # 快速移动
                move_cmd.velocity = min(gcode_cmd.f or 3000, 3000)  # 限制最大速度
                move_cmd.acc = 50
                move_cmd.dec = 50
            else:  # G1 工作进给
                move_cmd.velocity = min(gcode_cmd.f or 1000, 1000)  # 限制最大速度
                move_cmd.acc = 30
                move_cmd.dec = 30

            # 执行运动
            ret_code = nrc.robot_movel(self.socket_fd, move_cmd)

            if ret_code == 0:
                # 更新当前位置
                self.current_position = target_pos
                return True
            else:
                print(f"运动指令执行失败，返回码: {ret_code}")
                return False

        except Exception as e:
            print(f"执行G-code命令时发生异常: {e}")
            return False

    def execute_gcode_sequence(self, gcode_commands: List[GCodeCommand],
                             start_index: int = 0, max_commands: int = None) -> bool:
        """执行G-code命令序列"""
        print(f"\n--- 开始执行G-code轨迹 ---")

        if not gcode_commands:
            print("错误：没有G-code命令可执行")
            return False

        # 设置G-code原点
        if not self.set_gcode_origin(gcode_commands[0]):
            return False

        # 确定执行范围
        end_index = len(gcode_commands)
        if max_commands:
            end_index = min(start_index + max_commands, len(gcode_commands))

        print(f"将执行命令 {start_index+1} 到 {end_index}，共 {end_index - start_index} 条命令")

        success_count = 0

        for i in range(start_index, end_index):
            cmd = gcode_commands[i]
            print(f"\n执行命令 {i+1}/{end_index}: {cmd}")

            if self.execute_gcode_command(cmd):
                success_count += 1
                print(f"  ✅ 命令执行成功")

                # 添加运动间隔
                if cmd.command_type == 'G0':
                    time.sleep(0.5)  # 快速移动后短暂等待
                else:
                    time.sleep(0.8)  # 工作进给后稍长等待
            else:
                print(f"  ❌ 命令执行失败")

                # 询问是否继续
                user_input = input("是否继续执行剩余命令？(y/n): ").lower()
                if user_input != 'y':
                    break

        print(f"\n--- G-code轨迹执行完成 ---")
        print(f"成功执行: {success_count}/{end_index - start_index} 条命令")

        return success_count > 0

    def robot_power_off(self):
        """机器人安全下电"""
        print("\n--- 开始机器人下电流程 ---")
        try:
            nrc.set_servo_poweroff(self.socket_fd)
            time.sleep(1)
            print("✅ 机器人已安全下电。")
        except Exception as e:
            print(f"下电流程中发生异常: {e}")


def main():
    """主测试函数"""
    robot = None
    print("=" * 70)
    print("                G-CODE 轨迹执行器测试")
    print("=" * 70)

    # G-code文件路径
    gcode_file = os.path.join(PROJECT_ROOT, "jiyi.Gcode")

    print(f"\n📁 G-code文件路径: {gcode_file}")
    print("⚠️  警告：请确保机器人周围有足够的安全空间！")
    print("    本测试将解析并执行G-code文件中的轨迹")
    print("    🔄 G-code坐标将从毫米(mm)自动转换为米(m)单位")
    print("    📏 安全工作范围：±100mm (±0.1m)")
    print("    所有运动都将限制在安全工作范围内\n")

    try:
        # 1. 解析G-code文件
        print("步骤 1: 解析G-code文件...")
        parser = GCodeParser()
        gcode_commands = parser.parse_gcode_file(gcode_file)

        if not gcode_commands:
            print("❌ G-code文件解析失败或没有有效命令")
            return

        print(f"✅ 成功解析 {len(gcode_commands)} 条G-code命令")

        # 显示前几条命令预览
        print("\n前5条命令预览:")
        for i, cmd in enumerate(gcode_commands[:5]):
            print(f"  {i+1}: {cmd}")

        # 2. 连接机器人
        print(f"\n步骤 2: 连接机器人...")
        robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
        executor = GCodeExecutor(robot)

        # 3. 初始化并上电
        if not executor.robot_initialize_and_power_on():
            print("\n❌ 机器人未能成功上电，G-code执行无法继续。")
            return

        # 4. 移动到零点
        if not executor.move_to_home():
            print("\n❌ 机器人未能移动到零点位置。")
            return

        # 5. 询问执行范围
        print(f"\n步骤 3: 选择执行范围")
        print(f"总共有 {len(gcode_commands)} 条命令")
        print("⚠️  注意：G-code坐标已从毫米(mm)转换为米(m)单位")

        try:
            default_commands = min(30, len(gcode_commands))  # 默认执行前30条命令
            user_input = input(f"请输入要执行的命令数量（回车默认执行前{default_commands}条）: ")
            max_commands = int(user_input) if user_input else default_commands
            max_commands = min(max_commands, len(gcode_commands))
        except ValueError:
            max_commands = min(30, len(gcode_commands))

        print(f"将执行前 {max_commands} 条命令")

        # 确认执行
        confirm = input("\n确认开始执行G-code轨迹？(y/n): ").lower()
        if confirm != 'y':
            print("用户取消执行")
            return

        # 6. 执行G-code轨迹
        success = executor.execute_gcode_sequence(gcode_commands, max_commands=max_commands)

        if success:
            print("\n🎉 G-code轨迹执行完成！")
        else:
            print("\n❌ G-code轨迹执行失败。")

    except Exception as e:
        print(f"\n❌ 测试过程中发生意外错误: {e}")
    finally:
        # 7. 确保机器人下电并断开连接
        if robot:
            if nrc.get_connection_status(robot.socket_fd) == 0:
                executor.robot_power_off()
            robot.disconnect()
        print("\n测试结束。")

if __name__ == "__main__":
    main()
